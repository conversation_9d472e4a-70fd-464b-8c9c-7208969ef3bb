import 'package:chat_flutter_example/widgets/arc_clipper.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../ui/design_spec.dart';
import '../models/story_category.dart';
import '../models/story.dart';
import '../pages/story_detail_page.dart';
import '../pages/search_page.dart';
import '../widgets/story_category_chip.dart';
import '../widgets/guide_character.dart';
import '../widgets/unified_avatar.dart';
import '../services/user_profile_service.dart';
import '../services/guide_manager.dart';
import '../services/user_guide_service.dart';
import '../debug/guide_debug_page.dart';
import '../models/guide_step.dart';

class StoryBoardPage extends StatefulWidget {
  const StoryBoardPage({super.key});

  @override
  State<StoryBoardPage> createState() => _StoryBoardPageState();
}

class _StoryBoardPageState extends State<StoryBoardPage> {
  // 引导精准高亮：分类区与故事列表区
  final GlobalKey _categoriesKey = GlobalKey();
  final GlobalKey _storyListKey = GlobalKey();

  Rect? _calcRect(GlobalKey key) {
    try {
      final ctx = key.currentContext;
      if (ctx == null) {
        print('StoryboardPage: _calcRect ctx is null for key=$key');
        return null;
      }
      final box = ctx.findRenderObject() as RenderBox?;
      if (box == null || !box.attached) {
        print('StoryboardPage: _calcRect box invalid. box=$box attached=${box?.attached} for key=$key');
        return null;
      }
      final topLeft = box.localToGlobal(Offset.zero);
      final size = box.size;
      final rect = Rect.fromLTWH(topLeft.dx, topLeft.dy, size.width, size.height);
      print('StoryboardPage: calc rect -> $rect for key=$key, size=$size, topLeft=$topLeft');
      return rect;
    } catch (e) {
      print('StoryboardPage: calc rect error: $e for key=$key');
      return null;
    }
  }

  void _updateCategoryRectIfNeeded({String from = ''}) {
    if (!GuideManager.instance.isGuiding) return;
    final current = GuideManager.instance.getCurrentStep();
    if (current?.type != GuideStepType.categorySelection) return;

    void measure() {
      final rect = _calcRect(_categoriesKey);
      print('StoryboardPage: measure category rect (from=$from) -> $rect, isGuiding=${GuideManager.instance.isGuiding}');
      GuideManager.instance.updateHighlightRect(rect);
      GuideManager.instance.refreshCurrentStep();
    }

    // 立即、下一帧、轻延迟，三次测量覆盖布局抖动
    measure();
    WidgetsBinding.instance.addPostFrameCallback((_) => measure());
    Future.delayed(const Duration(milliseconds: 120), measure);
  }

  final TextEditingController _searchCtrl = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _loading = false;
  String? _error;

  List<StoryCategory> _categories = [];
  String _selectedCategoryId = '';
  List<Story> _allStories = [];

  // 引导相关状态
  bool _showGuideCharacter = false;
  bool _isScrolledDown = false;
  bool _guideEligible = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // 先检查新手引导完成状态，再加载数据，确保动画在数据加载完成回调中触发
      await _initGuideSystem();
      _initData();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _initData() async {
    setState(() {
      _loading = true;
      _error = null;
    });

    try {
      // 从JSON文件加载故事数据
      final String response =
          await rootBundle.loadString('assets/data/stories.json');
      final data = await json.decode(response);

      // 加载分类
      final List<dynamic> categoriesData = data['categories'];
      _categories = categoriesData.map((cat) {
        return StoryCategory(
          id: cat['id'],
          name: _getLocalizedString(cat['nameKey']),
          icon: _getIconFromString(cat['icon']),
          color: _parseColor(cat['color']),
        );
      }).toList();

      if (_categories.isNotEmpty) {
        _selectedCategoryId = _categories.first.id;
      }

      // 加载故事
      final List<dynamic> storiesData = data['stories'];
      _allStories = storiesData.map((story) {
        return Story(
          id: story['id'],
          title: Map<String, String>.from(story['title']),
          categoryId: story['categoryId'],
          imageUrl: story['imageUrl'],
          popularity: story['popularity'],
          backgroundSetting:
              Map<String, String>.from(story['backgroundSetting']),
          characterSetting: Map<String, String>.from(story['characterSetting']),
          currentPlot: Map<String, String>.from(story['currentPlot']),
          firstPlot: Map<String, String>.from(story['firstPlot']),
        );
      }).toList();
      GuideManager.instance.setCurrentStory(_allStories.first);
    } catch (e) {
      _error = AppLocalizations.of(context)!.loadFailed;
    } finally {
      setState(() {
        _loading = false;
        // 仅当引导未完成时显示，并在此触发入场动画
        _showGuideCharacter = true;
      });
      // 数据加载完成后尝试测量分类区域Rect
      _updateCategoryRectIfNeeded(from: 'initData-finally');
    }
  }

  /// 初始化引导系统
  Future<void> _initGuideSystem() async {
    final isCompleted = await UserGuideService.instance.isGuideCompleted();
    print('StoryboardPage: _initGuideSystem isCompleted=$isCompleted');
    setState(() {
      // 仅记录资格，不在此处触发动画，确保动画在数据加载完成回调中触发
      _guideEligible = !isCompleted;
    });
  }

  /// 滚动监听
  void _onScroll() {
    final isScrolledDown = _scrollController.offset > 100;
    if (isScrolledDown != _isScrolledDown) {
      setState(() {
        _isScrolledDown = isScrolledDown;
        _showGuideCharacter = !isScrolledDown;
      });
    }
  }

  bool _stepListenerAdded = false;

  /// 开始引导流程
  Future<void> _startGuide() async {
    print('StoryboardPage: _startGuide tapped, before startGuide. isGuiding=${GuideManager.instance.isGuiding}');
    await GuideManager.instance.startGuide(context);
    print('StoryboardPage: startGuide awaited. isGuiding=${GuideManager.instance.isGuiding}, currentStep=${GuideManager.instance.getCurrentStep()?.type}');

    // 监听步骤变化，按步骤设置高亮Rect（确保只注册一次）
    if (!_stepListenerAdded) {
      _stepListenerAdded = true;
      GuideManager.instance.stepNotifier.addListener(() {
        final type = GuideManager.instance.stepNotifier.value;
        final guiding = GuideManager.instance.isGuiding;
        print('StoryboardPage: stepNotifier fired. type=$type, isGuiding=$guiding');
        if (!guiding) return;
        if (type == GuideStepType.categorySelection) {
          _updateCategoryRectIfNeeded(from: 'stepNotifier');
        } else if (type == GuideStepType.storyList) {
          final rect = _calcRect(_storyListKey);
          print('StoryboardPage: storyList rect(after step change) -> $rect');
          GuideManager.instance.updateHighlightRect(rect);
        }
      });
      print('StoryboardPage: stepNotifier listener registered');
    }

    // 立即根据当前步骤设置一次rect，防止监听注册前已发送通知导致首次不精准
    final current = GuideManager.instance.getCurrentStep();
    final guiding = GuideManager.instance.isGuiding;
    print('StoryboardPage: checking initial step after startGuide. current=$current, isGuiding=$guiding');
    if (current != null && guiding) {
      if (current.type == GuideStepType.categorySelection) {
        _updateCategoryRectIfNeeded(from: 'startGuide-initial');
      } else if (current.type == GuideStepType.storyList) {
        final rect = _calcRect(_storyListKey);
        print('StoryboardPage: storyList rect(startGuide-initial) -> $rect');
        GuideManager.instance.updateHighlightRect(rect);
      }
    }
  }

  Color _parseColor(String hexColor) {
    return Color(int.parse(hexColor.substring(1), radix: 16) + 0xFF000000);
  }

  String _getLocalizedString(String key) {
    final localizations = AppLocalizations.of(context);
    switch (key) {
      case 'suspense':
        return localizations!.suspense;
      case 'sciFi':
        return localizations!.sciFi;
      case 'fantasy':
        return localizations!.fantasy;
      case 'history':
        return localizations!.history;
      case 'title_daily_1':
        return localizations!.storyEarlyDaysTitle;
      case 'desc_daily_1':
        return localizations!.storyEarlyDaysDesc;
      case 'title_adventure_1':
        return localizations!.storyHijrahTitle;
      case 'desc_adventure_1':
        return localizations!.storyHijrahDesc;
      case 'title_romance_1':
        return localizations!.storyPrincessTitle;
      case 'desc_romance_1':
        return localizations!.storyPrincessDesc;
      case 'title_fantasy_1':
        return localizations!.storyIbrahimTitle;
      case 'desc_fantasy_1':
        return localizations!.storyIbrahimDesc;
      case 'title_mystery_1':
        return localizations!.storyMusaTitle;
      case 'desc_mystery_1':
        return localizations!.storyMusaDesc;
      default:
        return key;
    }
  }

  IconData _getIconFromString(String iconName) {
    final iconMap = {
      'mystery': Icons.search,
      'science': Icons.science_outlined,
      'magic': Icons.auto_awesome,
      'history': Icons.history,
      'eco': Icons.eco,
      'park': Icons.park,
      'favorite': Icons.favorite,
      'person': Icons.person,
      'book': Icons.book,
      'star': Icons.star,
      'lightbulb': Icons.lightbulb,
      'school': Icons.school,
      'work': Icons.work,
      'home': Icons.home,
      'place': Icons.place,
      'public': Icons.public,
      'pets': Icons.pets,
      'nature': Icons.nature,
      'music': Icons.music_note,
      'art': Icons.brush,
      'sports': Icons.sports,
      'food': Icons.food_bank,
      'cake': Icons.cake,
      'icecream': Icons.icecream,
    };
    return iconMap[iconName] ?? Icons.book;
  }

  String _getLocalizedStringFromMap(Map<String, String> localizedMap) {
    final localizations = AppLocalizations.of(context);
    final String locale = localizations?.localeName ?? 'zh';
    return localizedMap[locale] ??
        localizedMap['zh'] ??
        localizedMap['en'] ??
        '';
  }

  List<Story> get _filteredStories {
    if (_allStories.isEmpty) return [];
    final q = _searchCtrl.text.trim().toLowerCase();
    final byCat = _allStories.where((s) =>
        _selectedCategoryId.isEmpty || s.categoryId == _selectedCategoryId);
    if (q.isEmpty) return byCat.toList();
    return byCat
        .where((s) =>
            _getLocalizedStringFromMap(s.title).toLowerCase().contains(q))
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (_error != null) {
      return Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(_error!),
            const SizedBox(height: 8),
            ElevatedButton(
                onPressed: _initData,
                child: Text(AppLocalizations.of(context)!.retry)),
          ],
        ),
      );
    }

    return Stack(
      children: [
        SafeArea(
          child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: RefreshIndicator(
                onRefresh: () async => _initData(),
                color: DesignSpec.primaryItemSelected,
                child: CustomScrollView(
                  controller: _scrollController,
                  physics: const BouncingScrollPhysics(
                      parent: AlwaysScrollableScrollPhysics()),
                  slivers: [
                    SliverToBoxAdapter(child: _buildTopBar()),
                    SliverToBoxAdapter(child: _buildSearchBar()),
                    SliverToBoxAdapter(child: _buildCategories()),
                    SliverToBoxAdapter(
                        child: _buildSectionTitle(
                            AppLocalizations.of(context)!.categoryStories)),
                    _buildStoryHoriList(_filteredStories),
                    SliverToBoxAdapter(
                        child: _buildSectionTitle(
                            AppLocalizations.of(context)!.mostPopular)),
                    _buildPopularList(),
                    const SliverToBoxAdapter(child: SizedBox(height: 100)),
                  ],
                ),
              )),
        ),
        // 引导角色
        GuideCharacter(
          isVisible: _showGuideCharacter,
          onTap: _startGuide,
          bubbleText: AppLocalizations.of(context)!.guideTooltip,
          showBubble: true,
        ),
      ],
    );
  }

  Widget _buildTopBar() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 16, 0, 8),
      child: Row(
        children: [
          Expanded(
            child: Text(
              AppLocalizations.of(context)!.appTitle,
              style: const TextStyle(
                fontSize: DesignSpec.fontSizeXl,
                fontWeight: DesignSpec.fontWeightBold,
              ),
            ),
          ),
          UnifiedAvatar(
            radius: 18,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 12),
      child: GestureDetector(
        onTap: () => _navigateToSearchPage(),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(0),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              )
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Row(
              children: [
                const Icon(Icons.search, color: Colors.black54),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    AppLocalizations.of(context)!.searchHint,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToSearchPage() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SearchPage(
          allStories: _allStories,
          categories: _categories,
          initialQuery: _searchCtrl.text,
        ),
      ),
    );
  }

  Widget _buildCategories() {
    if (_categories.isEmpty) {
      return const SizedBox(height: 0);
    }
    return Container(
      key: _categoriesKey,
      padding: const EdgeInsets.only(left: 0, right: 0, top: 8, bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context)!.storyCategories,
            style: const TextStyle(
                fontSize: DesignSpec.fontSizeBase,
                fontWeight: DesignSpec.fontWeightBold),
          ),
          const SizedBox(height: 12),
          Row(
            // key: _categoriesKey,
            children: [
              for (final c in _categories) ...[
                Expanded(
                  child: StoryCategoryChip(
                    icon: c.icon,
                    label: c.name,
                    color: c.color,
                    selected: c.id == _selectedCategoryId,
                    onTap: () => setState(() => _selectedCategoryId = c.id),
                  ),
                ),
                const SizedBox(width: 8),
              ]
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 4, 0, 8),
      child: Text(
        title,
        style: const TextStyle(
            fontSize: DesignSpec.fontSizeBase,
            fontWeight: DesignSpec.fontWeightBold),
      ),
    );
  }

  SliverToBoxAdapter _buildStoryHoriList(List<Story> stories) {
    return SliverToBoxAdapter(
      child: Container(
        key: _storyListKey,
        height: 200,
        padding: EdgeInsets.symmetric(vertical: 10),
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: stories.length,
          itemBuilder: (context, index) {
            final s = stories[index];
            return Padding(
              padding: const EdgeInsets.only(right: 16),
              child: SizedBox(
                width: 140,
                child: _StoryCard(story: s, allCategories: _categories),
              ),
            );
          },
        ),
      ),
    );
  }

  SliverList _buildPopularList() {
    if (_allStories.isEmpty) {
      return SliverList(delegate: SliverChildListDelegate([]));
    }
    _allStories.sort((a, b) => b.popularity.compareTo(a.popularity));
    final popular = _allStories.take(3).toList();
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 8),
            child: _PopularTile(story: popular[index], allCategories: _categories),
          );
        },
        childCount: popular.length,
      ),
    );
  }
}

class _StoryCard extends StatelessWidget {
  final Story story;
  final List<StoryCategory> _allCategories;

  const _StoryCard({required this.story, required List<StoryCategory> allCategories}) : _allCategories = allCategories;

  String _getLocalizedStringFromMap(
      BuildContext context, Map<String, String> localizedMap) {
    final localizations = AppLocalizations.of(context);
    final String locale = localizations?.localeName ?? 'zh';
    return localizedMap[locale] ??
        localizedMap['zh'] ??
        localizedMap['en'] ??
        '';
  }

  void _onStoryTap(BuildContext context) async {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StoryDetailPage(
          story: story,
          categories: _allCategories,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () => _onStoryTap(context),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            ClipArcRRect(
              arcHeight: -6,
              borderRadius: 30,
              child: Image.asset(
                story.imageUrl,
                width: 140,
                height: 140,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.blue.shade300, Colors.purple.shade300],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.auto_stories,
                        color: Colors.white,
                        size: 40,
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 8),
            Padding(
                padding: EdgeInsets.symmetric(horizontal: 8),
                child: Text(
                  _getLocalizedStringFromMap(context, story.title),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                      fontSize: 13, fontWeight: DesignSpec.fontWeightSemiBold),
                )),
          ],
        ));
  }
}

class _PopularTile extends StatelessWidget {
  final Story story;
  final List<StoryCategory> _allCategories;

  const _PopularTile({required this.story, required List<StoryCategory> allCategories}) : _allCategories = allCategories;

  String _getLocalizedStringFromMap(
      BuildContext context, Map<String, String> localizedMap) {
    final localizations = AppLocalizations.of(context);
    final String locale = localizations?.localeName ?? 'zh';
    return localizedMap[locale] ??
        localizedMap['zh'] ??
        localizedMap['en'] ??
        '';
  }

  void _onPopularStoryTap(BuildContext context) async {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StoryDetailPage(
          story: story,
          categories: _allCategories,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () => _onPopularStoryTap(context),
        child: Stack(
          children: [
            Positioned.fill(
              top: 30,
              child: Container(
                height: 680,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 使用BulgeClipWidget实现枕头形状的图片裁剪效果
                ClipArcRRect(
                  arcHeight: 12,
                  borderRadius: 25,
                  child: Image.asset(
                    story.imageUrl,
                    width: double.infinity,
                    height: 180,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: double.infinity,
                        height: 120,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [Colors.grey.shade300, Colors.grey.shade400],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.image_not_supported,
                            color: Colors.white,
                            size: 40,
                          ),
                        ),
                      );
                    },
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 6),
                  child: Row(
                    children: [
                      Text(
                        _getLocalizedStringFromMap(context, story.title),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.start,
                        style: const TextStyle(
                          fontWeight: DesignSpec.fontWeightSemiBold,
                          fontSize: DesignSpec.fontSizeBase,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.star,
                            color: Colors.amber.shade600,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${story.popularity}',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 6),
                  child: Text(
                    _getLocalizedStringFromMap(context, story.backgroundSetting),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.start,
                    style: const TextStyle(
                      color: Colors.grey,
                      fontWeight: DesignSpec.fontWeightMedium,
                      fontSize: DesignSpec.fontSizeSm,
                    ),
                  ),
                ),
                SizedBox(height: 15),
              ],
            )
          ],
        ));
  }
}
