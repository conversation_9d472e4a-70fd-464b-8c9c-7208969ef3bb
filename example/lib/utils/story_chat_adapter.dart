import '../models/plot.dart';
import '../models/story.dart';
import '../models/chat_session.dart';
import '../models/message.dart';
import '../utils/plot_parser.dart';

class StoryChatAdapter {
  /// 将Story对象转换为ChatSession
  static ChatSession createChatSessionFromStory(Story story, String locale) {
    final title = _getLocalizedString(story.title, locale);
    final background = _getLocalizedString(story.backgroundSetting, locale);
    final character = _getLocalizedString(story.characterSetting, locale);
    final plot = _getLocalizedString(story.currentPlot, locale);

    final initialMessage = Message.plotMessage(
      id: 'initial_${story.id}_${DateTime.now().millisecondsSinceEpoch}',
      content: plot,
    );

    // 获取本地化的firstPlot数据
    final firstPlotData = story.firstPlot[locale] ?? story.firstPlot['zh'];
    // 使用PlotParser解析firstPlot数据并生成消息
    final baseId = DateTime.now().millisecondsSinceEpoch.toString();
    final parseResult = PlotParseResult.fromPlot(Plot.fromJson(firstPlotData));
    final messageResult = PlotParser.generateMessagesFromPlot(parseResult, baseId);
    messageResult.messages.insert(0, initialMessage);

    return ChatSession(
      id: 'story_${story.id}_${DateTime.now().millisecondsSinceEpoch}',
      title: title,
      avatarUrl: story.imageUrl,
      messages: messageResult.messages,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      storyId: story.id,
    );
  }
  
  /// 获取本地化的字符串
  static String _getLocalizedString(Map<String, String> localizedMap, String locale) {
    return localizedMap[locale] ?? localizedMap['zh'] ?? localizedMap['en'] ?? '';
  }
}