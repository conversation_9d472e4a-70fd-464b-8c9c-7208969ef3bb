class Story {
  final String id;
  final Map<String, String> title;
  final String categoryId;
  final String imageUrl;
  final int popularity; // 用于"热门故事"排序
  final Map<String, String> backgroundSetting;
  final Map<String, String> characterSetting;
  final Map<String, String> currentPlot;
  final Map<String, dynamic> firstPlot; // 预设的第一个剧情（包含完整的Plot结构）

  const Story({
    required this.id,
    required this.title,
    required this.categoryId,
    required this.imageUrl,
    this.popularity = 0,
    required this.backgroundSetting,
    required this.characterSetting,
    required this.currentPlot,
    required this.firstPlot,
  });
}

