import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../models/story.dart';

class StoryDataService {
  static final StoryDataService _instance = StoryDataService._internal();
  static StoryDataService get instance => _instance;
  
  StoryDataService._internal();
  
  List<Story> _allStories = [];
  bool _isLoading = false;
  String? _error;
  
  List<Story> get allStories => _allStories;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  Future<void> loadStories() async {
    if (_allStories.isNotEmpty) return;
    
    _isLoading = true;
    _error = null;
    
    try {
      final String response = await rootBundle.loadString('assets/data/stories.json');
      final data = await json.decode(response);
      
      final List<dynamic> storiesData = data['stories'];
      _allStories = storiesData.map((story) {
        return Story(
          id: story['id'],
          title: Map<String, String>.from(story['title']),
          categoryId: story['categoryId'],
          imageUrl: story['imageUrl'],
          popularity: story['popularity'],
          backgroundSetting: Map<String, String>.from(story['backgroundSetting']),
          characterSetting: Map<String, String>.from(story['characterSetting']),
          currentPlot: Map<String, String>.from(story['currentPlot']),
          firstPlot: Map<String, String>.from(story['firstPlot']),
        );
      }).toList();
    } catch (e) {
      _error = 'Failed to load stories: $e';
    } finally {
      _isLoading = false;
    }
  }
  
  Story? getStoryById(String id) {
    try {
      return _allStories.firstWhere((story) => story.id == id);
    } catch (e) {
      return null;
    }
  }
  
  String getStoryTitle(String storyId, String locale) {
    final story = getStoryById(storyId);
    if (story == null) return '';
    
    return story.title[locale] ?? story.title['zh'] ?? story.title['en'] ?? '';
  }
  
  String getStoryTitleFromContext(String storyId, BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final String locale = localizations.localeName;
    return getStoryTitle(storyId, locale);
  }
  
  void clearCache() {
    _allStories = [];
    _error = null;
    _isLoading = false;
  }
}