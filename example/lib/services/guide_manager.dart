import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../models/guide_step.dart';
import '../widgets/app_dialogs.dart';
import '../models/story.dart';
import '../models/story_category.dart';
import '../pages/story_detail_page.dart';
import '../pages/chat_page.dart';
import '../models/chat_session.dart';
import '../config/guide_config.dart';
import '../utils/story_chat_adapter.dart';
import 'chat_session_manager.dart';
import 'user_guide_service.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// 引导管理器
class GuideManager {
  static GuideManager? _instance;
  static GuideManager get instance {
    _instance ??= GuideManager._();
    return _instance!;
  }
  
  GuideManager._();
  
  GuideState _currentState = GuideState.hidden;
  int _currentStepIndex = 0;
  BuildContext? _context;
  OverlayEntry? _overlayEntry;

  // 页面跳转后的延迟显示回调
  VoidCallback? _pendingShowCallback;

  // 当前正在引导的故事信息
  Story? _currentStory;

  // 步骤变化通知（用于跨页面联动，例如详情页滚动到按钮再显示高亮）
  final ValueNotifier<GuideStepType?> stepNotifier = ValueNotifier<GuideStepType?>(null);
  
  GuideState get currentState => _currentState;
  int get currentStepIndex => _currentStepIndex;
  
  /// 引导步骤定义
  List<GuideStep> get _steps => GuideConfig.steps;
  
  /// 开始引导流程
  Future<void> startGuide(BuildContext context) async {
    // 检查是否已完成引导
    final isCompleted = await UserGuideService.instance.isGuideCompleted();
    print('GuideManager.startGuide: isCompleted=$isCompleted, currentState=$_currentState');
    if (isCompleted) {
      // 如果已完成引导，显示确认对话框
      final shouldRestart = await _showRestartConfirmDialog(context);
      print('GuideManager.startGuide: shouldRestart=$shouldRestart');
      if (!shouldRestart) return;
    }

    print('GuideManager: Starting guide flow');
    _context = context;
    _currentState = GuideState.guiding;
    _currentStepIndex = 0;

    print('GuideManager.startGuide: set state to guiding, stepIndex=$_currentStepIndex');
    _showCurrentStep();
  }

  /// 显示重新开始引导的确认对话框
  Future<bool> _showRestartConfirmDialog(BuildContext context) async {
    final l10n = AppLocalizations.of(context)!;
    return await AppDialogs.showConfirmDialog(
      context: context,
      title: l10n.restartGuideTitle,
      content: l10n.restartGuideContent,
      confirmText: l10n.restart,
      cancelText: l10n.cancel,
    ) ?? false;
  }

  /// 更新引导上下文（用于页面跳转后继续引导）
  void updateContext(BuildContext context) {
    print('GuideManager: Updating context, current state: $_currentState, step: ${_currentStepIndex + 1}');
    _context = context;

    // 如果有待显示的引导，立即显示
    if (_pendingShowCallback != null) {
      print('GuideManager: Executing pending show callback');
      final callback = _pendingShowCallback!;
      _pendingShowCallback = null;
      callback();
    } else {
      print('GuideManager: No pending show callback');
    }
  }

  /// 在指定延迟后显示引导（用于页面跳转场景）
  void showGuideAfterDelay({Duration delay = const Duration(milliseconds: 800)}) {
    print('GuideManager: Setting up delayed guide show with delay: ${delay.inMilliseconds}ms');
    _pendingShowCallback = () {
      print('GuideManager: Executing delayed show callback, state: $_currentState');
      if (_currentState == GuideState.guiding) {
        _showCurrentStep();
      }
    };

    Future.delayed(delay, () {
      print('GuideManager: Delay completed, checking conditions - pendingCallback: ${_pendingShowCallback != null}, context: ${_context != null}');
      if (_pendingShowCallback != null && _context != null) {
        final callback = _pendingShowCallback!;
        _pendingShowCallback = null;
        callback();
      } else {
        print('GuideManager: Cannot execute delayed callback - conditions not met');
      }
    });
  }
  
  /// 显示当前步骤
  void _showCurrentStep() {
    if (_context == null || _currentStepIndex >= _steps.length) {
      print('GuideManager: Cannot show step - context: $_context, stepIndex: $_currentStepIndex, stepsLength: ${_steps.length}');
      return;
    }

    final step = _steps[_currentStepIndex];
    print('GuideManager: Showing step ${_currentStepIndex + 1}: ${step.type}');
    final updatedStep = _updateStepWithDynamicData(step);
    _showOverlay(updatedStep);
  }

  Rect? _overrideRect;

  /// 外部更新高亮区域（由页面使用GlobalKey计算后调用）
  void updateHighlightRect(Rect? rect) {
    _overrideRect = rect;
    print('GuideManager: updateHighlightRect -> $_overrideRect');
  }

  /// 根据当前页面状态更新步骤数据
  GuideStep _updateStepWithDynamicData(GuideStep step) {
    if (_context == null) return step;

    final screenSize = MediaQuery.of(_context!).size;
    final rect = _overrideRect ?? GuideConfig.getHighlightRect(step.type, screenSize);
    final fingerPosition = GuideConfig.getFingerPosition(rect, step.animationType);

    return GuideStep(
      type: step.type,
      title: step.title,
      description: step.description,
      animationType: step.animationType,
      highlightRect: rect,
      fingerPosition: fingerPosition,
    );
  }

  /// 手动刷新当前步骤的遮罩（当页面后来计算出更精准的Rect时调用）
  void refreshCurrentStep() {
    print('GuideManager: refreshCurrentStep requested. state=$_currentState, stepIndex=$_currentStepIndex');
    if (_currentState == GuideState.guiding) {
      _showCurrentStep();
    }
  }

  /// 显示遮罩层
  void _showOverlay(GuideStep step) {
    _removeOverlay();
    
    _overlayEntry = OverlayEntry(
      builder: (context) => _buildOverlayContent(step),
    );
    
    Overlay.of(_context!).insert(_overlayEntry!);
  }
  
  /// 构建遮罩内容
  Widget _buildOverlayContent(GuideStep step) {
    return Material(
      color: Colors.transparent,
      child: Stack(
        children: [
          // 黑色半透明遮罩
          Container(
            width: double.infinity,
            height: double.infinity,
            color: Colors.black.withOpacity(0.7),
          ),
          
          // 镂空高亮区域
          if (step.highlightRect != null)
            _buildHighlightArea(step.highlightRect!),
          
          // 手指动画
          if (step.fingerPosition != null)
            _buildFingerAnimation(step),
          
          // 提示内容
          _buildTipContent(step),
          
          // 跳过按钮
          _buildSkipButton(),
        ],
      ),
    );
  }
  
  Widget _buildHighlightArea(Rect rect) {
    return Positioned(
      left: rect.left,
      top: rect.top,
      width: rect.width,
      height: rect.height,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.transparent,
          border: Border.all(color: Colors.white, width: 2),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.white.withOpacity(0.3),
              blurRadius: 10,
              spreadRadius: 2,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildFingerAnimation(GuideStep step) {
    return Positioned(
      left: step.fingerPosition!.dx,
      top: step.fingerPosition!.dy,
      child: _FingerAnimation(type: step.animationType),
    );
  }
  
  Widget _buildTipContent(GuideStep step) {
    return Positioned(
      bottom: 120,
      left: 24,
      right: 24,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              step.title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              step.description,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black54,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${_currentStepIndex + 1}/${_steps.length}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
                _buildActionButton(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建动作按钮（下一步或完成）
  Widget _buildActionButton() {
    if (_currentStepIndex < _steps.length - 1) {
      // 不是最后一步，显示"下一步"按钮
      return TextButton(
        onPressed: nextStep,
        child: const Text('下一步'),
      );
    } else {
      // 最后一步，显示"我知道了"按钮
      return ElevatedButton(
        onPressed: completeGuide,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
        child: const Text('我知道了'),
      );
    }
  }

  Widget _buildSkipButton() {
    return Positioned(
      top: MediaQuery.of(_context!).padding.top + 16,
      right: 24,
      child: GestureDetector(
        onTap: skipGuide,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.9),
            borderRadius: BorderRadius.circular(20),
          ),
          child: const Text(
            '跳过',
            style: TextStyle(
              fontSize: 14,
              color: Colors.black87,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
  
  /// 下一步
  void nextStep() {
    print('GuideManager: Moving to next step from ${_currentStepIndex + 1}');
    _currentStepIndex++;

    if (_currentStepIndex >= _steps.length) {
      print('GuideManager: Reached end of steps, completing guide');
      completeGuide();
      return;
    }

    final type = _steps[_currentStepIndex].type;
    print('GuideManager: Now at step ${_currentStepIndex + 1}: $type, notifying listeners');
    // 通知监听者（详情页/聊天页）
    stepNotifier.value = type;
    print('GuideManager: stepNotifier.value set to $type');
    // 根据步骤类型执行相应的页面跳转
    _handleStepNavigation();
  }
  
  /// 处理步骤导航
  void _handleStepNavigation() {
    final step = _steps[_currentStepIndex];
    print('GuideManager._handleStepNavigation: step=${step.type}, index=${_currentStepIndex + 1}');

    switch (step.type) {
      case GuideStepType.categorySelection:
        print('GuideManager: showing categorySelection on storyboard');
        _showCurrentStep();
        break;
      case GuideStepType.storyList:
        print('GuideManager: showing storyList on storyboard');
        _showCurrentStep();
        break;
      case GuideStepType.storyDetail:
        print('GuideManager: navigate to storyDetail');
        _removeOverlay();
        _navigateToStoryDetail();
        break;
      case GuideStepType.startStory:
        print('GuideManager: will show startStory after delay on detail page');
        showGuideAfterDelay();
        break;
      case GuideStepType.chatInteraction:
        print('GuideManager: navigate to chat and delay show');
        _removeOverlay();
        _navigateToChat();
        break;
    }
  }
  
  /// 导航到故事详情页
  void _navigateToStoryDetail() {
    if (_context == null) return;

    // 创建测试故事分类
    final testCategories = [
      StoryCategory(
        id: 'suspense',
        name: '悬疑',
        icon: Icons.psychology,
        color: Colors.purple,
      ),
      StoryCategory(
        id: 'sci-fi',
        name: '科幻',
        icon: Icons.rocket_launch,
        color: Colors.blue,
      ),
      StoryCategory(
        id: 'fantasy',
        name: '奇幻',
        icon: Icons.auto_awesome,
        color: Colors.green,
      ),
    ];


    if (_currentStory != null) {
      Navigator.push(
        _context!,
        MaterialPageRoute(
          builder: (context) => StoryDetailPage(
            story: _currentStory!,
            categories: testCategories,
          ),
        ),
      ).then((_) {
        // 页面返回后，如果引导还在进行中，返回到故事板页面的引导
        if (_currentState == GuideState.guiding) {
          // 不需要特殊处理，用户返回到故事板页面
        }
      });
    }

  }
  
  /// 提供给外界设置当前故事（由详情页在进入时调用）
  void setCurrentStory(Story story) {
    _currentStory = story;
    print('GuideManager: setCurrentStory id=${story.id}');
  }

  /// 导航到聊天页面
  void _navigateToChat() async {
    if (_context == null || _currentStory == null) {
      print('GuideManager: Cannot navigate to chat - context: $_context, story: $_currentStory');
      return;
    }

    print('GuideManager: Creating chat session for story: ${_currentStory!.id}');

    final localizations = AppLocalizations.of(_context!);
    final locale = localizations?.localeName ?? 'en';

    // 检查是否有保存的会话
    final existingSession = await ChatSessionManager.instance.getLatestSessionForStory(_currentStory!.id);

    ChatSession chatSession;
    if (existingSession != null) {
      // 使用已保存的会话
      chatSession = existingSession;
    } else {
      // 创建新会话
      chatSession = StoryChatAdapter.createChatSessionFromStory(_currentStory!, locale);
    }


    Navigator.push(
      _context!,
      MaterialPageRoute(
        builder: (context) => ChatPage(session: chatSession),
      ),
    ).then((_) {
      // 聊天页面返回后完成引导
      completeGuide();
    });

    // 设置在聊天页面显示引导的延迟回调
    showGuideAfterDelay(delay: const Duration(milliseconds: 1200));
  }
  
  /// 跳过引导
  void skipGuide() {
    print('GuideManager: Skipping guide');
    _removeOverlay();
    _currentState = GuideState.completed;
    // 跳过时不标记为已完成，这样用户下次还能看到引导

    // 导航回到故事板页面
    _navigateBackToStoryboard();
  }
  
  /// 完成引导
  void completeGuide() {
    print('GuideManager: Completing guide');
    _removeOverlay();
    _currentState = GuideState.completed;
    UserGuideService.instance.markGuideCompleted();

    // 如果在引导过程中跳转了页面，返回到故事板页面
    // _navigateBackToStoryboard();
  }

  /// 导航回到故事板页面
  void _navigateBackToStoryboard() {
    if (_context != null) {
      try {
        print('GuideManager: Navigating back to storyboard');
        // 使用popUntil返回到第一个页面（故事板页面）
        Navigator.of(_context!).popUntil((route) => route.isFirst);
        print('GuideManager: Successfully navigated back to storyboard');
      } catch (e) {
        print('GuideManager: Error navigating back to storyboard: $e');
        // 如果popUntil失败，尝试直接pop到根页面
        try {
          Navigator.of(_context!).popUntil(ModalRoute.withName('/'));
        } catch (e2) {
          print('GuideManager: Fallback navigation also failed: $e2');
        }
      }
    } else {
      print('GuideManager: Cannot navigate back - context is null');
    }
  }

  /// 重置引导状态（用于测试）
  Future<void> resetGuide() async {
    await UserGuideService.instance.resetGuideStatus();
    _currentState = GuideState.hidden;
    _currentStepIndex = 0;
    _removeOverlay();
  }

  /// 获取当前步骤信息
  GuideStep? getCurrentStep() {
    if (_currentStepIndex < _steps.length) {
      return _steps[_currentStepIndex];
    }
    return null;
  }

  /// 检查是否正在引导中
  bool get isGuiding => _currentState == GuideState.guiding;
  
  /// 移除遮罩层
  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
  

}

/// 手指动画组件
class _FingerAnimation extends StatefulWidget {
  final FingerAnimationType type;
  
  const _FingerAnimation({required this.type});

  @override
  State<_FingerAnimation> createState() => _FingerAnimationState();
}

class _FingerAnimationState extends State<_FingerAnimation>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.translate(
          offset: _getAnimationOffset(),
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.9),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              _getFingerIcon(),
              color: Colors.black87,
              size: 20,
            ),
          ),
        );
      },
    );
  }
  
  Offset _getAnimationOffset() {
    switch (widget.type) {
      case FingerAnimationType.tap:
        final scale = 1.0 + (_animation.value * 0.1);
        return Offset(0, (1 - scale) * 20);
      case FingerAnimationType.swipeUp:
        return Offset(0, -_animation.value * 50);
    }
  }
  
  IconData _getFingerIcon() {
    switch (widget.type) {
      case FingerAnimationType.tap:
        return Icons.touch_app;
      case FingerAnimationType.swipeUp:
        return Icons.keyboard_arrow_up;
    }
  }
}
