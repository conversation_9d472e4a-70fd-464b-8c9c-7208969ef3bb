{"hooks": {"Notification": [{"matcher": "", "hooks": [{"type": "command", "command": "afplay $CLAUDE_PROJECT_DIR/sounds/confield_chase.mp3"}]}], "PostToolUse": [{"matcher": "Edit|MultiEdit|Write", "hooks": [{"type": "command", "command": "claude -p '检查刚刚被修改的文件，将里面所有硬编码的中文字符串，都改为符合 i18next 规范的国际化实现。"}]}], "PreToolUse": [{"matcher": "Edit|MultiEdit|Write", "hooks": [{"type": "command", "command": "python3 -c \\\"import json, sys; data=json.load(sys.stdin); path=data.get('tool_input',{}).get('file_path',''); sys.exit(2 if any(p in path for p in ['.env', 'package-lock.json', '.git/']) else 0)\\\""}]}]}}