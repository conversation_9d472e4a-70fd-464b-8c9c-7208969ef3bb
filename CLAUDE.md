# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 提供在此代码库中工作的指导。

## 项目概述
这是一个 Flutter 插件 (`chat_flutter`)，包含一个功能完善的示例应用，实现了交互式故事聊天应用。项目采用双重结构：核心插件（含 FFI 绑定）和功能完整的示例应用。

## 关键命令

### Flutter 开发（使用 FVM）
```bash
# 安装依赖
fvm flutter pub get

# 运行示例应用
fvm flutter run

# 生产环境构建
fvm flutter build apk
fvm flutter build ios

# 运行测试
fvm flutter test
fvm flutter test integration_test/

# 生成代码
fvm flutter pub run build_runner build
```

### 原生开发
```bash
# 生成 FFI 绑定
dart run ffigen --config ffigen.yaml
```

## 架构概述

### 核心模式
- **服务模式**：单例服务用于状态管理（`ChatSessionManager`、`UserInteractionService`、`StoryDataService`）
- **离线优先**：SQLite 本地存储，SharedPreferences 用于用户偏好设置
- **国际化**：Flutter 本地化多语言支持（英语、中文、阿拉伯语、印地语）
- **设计系统**：通过 `DesignSpec` 类集中管理设计令牌

### 目录结构
```
├── src/                    # FFI 的本地 C 代码
├── example/               # 功能完整的演示应用
│   ├── lib/
│   │   ├── models/        # 数据模型（Message、Story、ChatSession）
│   │   ├── services/      # 业务逻辑服务
│   │   ├── pages/         # 主界面（聊天、故事板、个人资料）
│   │   ├── widgets/       # 可复用的 UI 组件
│   │   ├── ui/           # 设计系统（DesignSpec）
│   │   └── network/      # API 通信
│   ├── test/             # 单元测试和组件测试
│   └── assets/           # 静态资源和数据
```

## 关键服务
- `ChatSessionManager`：管理聊天会话和消息持久化
- `UserInteractionService`：处理点赞/收藏，使用 SQLite 存储
- `StoryDataService`：从 JSON 资源加载故事数据
- `OnboardingService`：管理首次用户体验

## 重要文件
- `example/lib/ui/design_spec.dart`：集中式设计系统
- `example/lib/services/chat_session_manager.dart`：核心聊天功能
- `example/lib/models/message.dart`：消息数据结构
- `ffigen.yaml`：FFI 绑定配置
- `src/chat_flutter.c`：本地 C 实现

## 开发注意事项
- 始终为 Flutter 命令添加 FVM 前缀
- 遵循服务模式处理新的业务逻辑
- 实现新功能时考虑国际化
- 使用现有的测试模式（单元测试使用 SharedPreferences 模拟）
- 新 UI 组件应使用 DesignSpec 保持一致样式